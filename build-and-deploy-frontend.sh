#!/bin/bash

echo "========================================"
echo "Building and Deploying Frontend Apps"
echo "========================================"

# Set environment variables for mDNS hostname
export REACT_APP_API_BASE_URL=http://restaurant.local:8080

echo ""
echo "1. Building Admin Panel..."
echo "========================================"
cd admin-panel
if [ -d "build" ]; then
    rm -rf build
fi
npm run build
if [ $? -ne 0 ]; then
    echo "ERROR: Admin Panel build failed!"
    exit 1
fi
cd ..

echo ""
echo "2. Building Entity Dashboard..."
echo "========================================"
cd entity-dashboard
if [ -d "build" ]; then
    rm -rf build
fi
npm run build
if [ $? -ne 0 ]; then
    echo "ERROR: Entity Dashboard build failed!"
    exit 1
fi
cd ..

echo ""
echo "3. Preparing Backend Static Resources..."
echo "========================================"
mkdir -p "src/main/resources/static"
if [ -d "src/main/resources/static/admin" ]; then
    rm -rf "src/main/resources/static/admin"
fi
if [ -d "src/main/resources/static/entity" ]; then
    rm -rf "src/main/resources/static/entity"
fi

echo ""
echo "4. Copying Admin Panel Build..."
echo "========================================"
cp -r "admin-panel/build" "src/main/resources/static/admin"
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to copy Admin Panel build!"
    exit 1
fi

echo ""
echo "5. Copying Entity Dashboard Build..."
echo "========================================"
cp -r "entity-dashboard/build" "src/main/resources/static/entity"
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to copy Entity Dashboard build!"
    exit 1
fi

echo ""
echo "========================================"
echo "SUCCESS: Frontend apps built and deployed!"
echo "========================================"
echo ""
echo "Access URLs (after starting backend):"
echo "- Admin Panel: http://restaurant.local:8080/admin"
echo "- Entity Dashboard: http://restaurant.local:8080/entity"
echo "- API Discovery: http://restaurant.local:8080/subscriber/discovery"
echo ""
echo "Note: Make sure the backend is running on port 8080"
echo "      and mDNS service is advertising as 'restaurant.local'"
echo ""
