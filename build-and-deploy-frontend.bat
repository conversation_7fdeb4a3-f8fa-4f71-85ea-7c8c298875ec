@echo off
echo ========================================
echo Building and Deploying Frontend Apps
echo ========================================

REM Set environment variables for mDNS hostname
set REACT_APP_API_BASE_URL=http://restaurant.local:8080

echo.
echo 1. Building Admin Panel...
echo ========================================
cd admin-panel
if exist build rmdir /s /q build
call npm run build
if %ERRORLEVEL% neq 0 (
    echo ERROR: Admin Panel build failed!
    pause
    exit /b 1
)
cd ..

echo.
echo 2. Building Entity Dashboard...
echo ========================================
cd entity-dashboard
if exist build rmdir /s /q build
call npm run build
if %ERRORLEVEL% neq 0 (
    echo ERROR: Entity Dashboard build failed!
    pause
    exit /b 1
)
cd ..

echo.
echo 3. Preparing Backend Static Resources...
echo ========================================
if not exist "src\main\resources\static" mkdir "src\main\resources\static"
if exist "src\main\resources\static\admin" rmdir /s /q "src\main\resources\static\admin"
if exist "src\main\resources\static\entity" rmdir /s /q "src\main\resources\static\entity"

echo.
echo 4. Copying Admin Panel Build...
echo ========================================
xcopy "admin-panel\build\*" "src\main\resources\static\admin\" /E /I /Y
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to copy Admin Panel build!
    pause
    exit /b 1
)

echo.
echo 5. Copying Entity Dashboard Build...
echo ========================================
xcopy "entity-dashboard\build\*" "src\main\resources\static\entity\" /E /I /Y
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to copy Entity Dashboard build!
    pause
    exit /b 1
)

echo.
echo ========================================
echo SUCCESS: Frontend apps built and deployed!
echo ========================================
echo.
echo Access URLs (after starting backend):
echo - Admin Panel: http://restaurant.local:8080/admin
echo - Entity Dashboard: http://restaurant.local:8080/entity
echo - API Discovery: http://restaurant.local:8080/subscriber/discovery
echo.
echo Note: Make sure the backend is running on port 8080
echo       and mDNS service is advertising as 'restaurant.local'
echo.
pause
