import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  Avatar,
  LinearProgress,
  Chip,
  IconButton,
  Tooltip,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Divider,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
  Snackbar,
} from '@mui/material';
import {
  Business,
  People,
  TrendingUp,
  Security,
  Nfc,
  Assessment,
  Refresh,
  CheckCircle,
  Info,
  Schedule,
  DeleteForever,
  Warning,
} from '@mui/icons-material';
import ApiService from '../services/ApiService';

interface DashboardStats {
  totalOrganizations: number;
  totalEntityAdmins: number;
  totalSuperAdmins: number;
  totalNfcCards: number;
  systemHealth: {
    status: 'healthy' | 'warning' | 'error';
    uptime: string;
    lastBackup: string;
  };
  recentActivity: ActivityItem[];
  systemMetrics: {
    totalUsers: number;
    totalSessions: number;
    totalOrders: number;
    systemLoad: number;
  };
}

interface ActivityItem {
  id: number;
  type: 'organization' | 'admin' | 'system' | 'security';
  message: string;
  timestamp: string;
  severity: 'info' | 'warning' | 'error' | 'success';
}

const AdminDashboardOverview: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Database cleanup states
  const [cleanupDialogOpen, setCleanupDialogOpen] = useState(false);
  const [cleanupLoading, setCleanupLoading] = useState(false);
  const [cleanupSnackbar, setCleanupSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'info',
  });

  const fetchDashboardStats = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Fetch all required data
      const [orgsResponse, adminsResponse, superAdminsResponse] = await Promise.all([
        ApiService.get('/super/entities'),
        ApiService.get('/super/entity-admins'),
        ApiService.get('/super/super-admins'),
      ]);

      // Fetch additional real data
      const [nfcCardsResponse, systemMetricsResponse, activityResponse] = await Promise.all([
        ApiService.get('/super/nfc-cards').catch(() => ({ data: [{ count: 0 }] })),
        ApiService.get('/super/system-metrics').catch(() => ({ data: { totalUsers: 0, totalSessions: 0, totalOrders: 0, systemLoad: 0 } })),
        ApiService.get('/super/recent-activity').catch(() => ({ data: [] })),
      ]);

      // Calculate real statistics
      const realStats: DashboardStats = {
        totalOrganizations: orgsResponse.data?.length || 0,
        totalEntityAdmins: adminsResponse.data?.length || 0,
        totalSuperAdmins: superAdminsResponse.data?.length || 0,
        totalNfcCards: nfcCardsResponse.data?.[0]?.count || nfcCardsResponse.data?.length || 0,
        systemHealth: {
          status: 'healthy',
          uptime: '99.9%',
          lastBackup: new Date().toLocaleString(),
        },
        recentActivity: activityResponse.data?.slice(0, 4) || [
          {
            id: 1,
            type: 'system',
            message: 'System is running smoothly',
            timestamp: new Date().toLocaleString(),
            severity: 'success',
          }
        ],
        systemMetrics: {
          totalUsers: systemMetricsResponse.data?.totalUsers || 0,
          totalSessions: systemMetricsResponse.data?.totalSessions || 0,
          totalOrders: systemMetricsResponse.data?.totalOrders || 0,
          systemLoad: systemMetricsResponse.data?.systemLoad || 0,
        },
      };

      setStats(realStats);
      setLastUpdated(new Date());
    } catch (err: any) {
      console.error('Failed to fetch dashboard stats:', err);
      setError('Failed to load dashboard statistics. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardStats();
    
    // Auto-refresh every 5 minutes
    const interval = setInterval(fetchDashboardStats, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'organization': return <Business />;
      case 'admin': return <People />;
      case 'system': return <Assessment />;
      case 'security': return <Security />;
      default: return <Info />;
    }
  };

  const getActivityColor = (severity: string) => {
    switch (severity) {
      case 'success': return 'success';
      case 'warning': return 'warning';
      case 'error': return 'error';
      default: return 'info';
    }
  };

  const handleRefresh = () => {
    fetchDashboardStats();
  };

  const handleCleanupDatabase = async () => {
    console.log('🚨 Database cleanup initiated by user');
    setCleanupLoading(true);

    try {
      console.log('🔄 Sending cleanup request to backend...');
      const response = await ApiService.post('/super/database/cleanup', {});

      console.log('✅ Cleanup response:', response.data);

      if (response.data.success) {
        setCleanupSnackbar({
          open: true,
          message: `Database cleanup completed successfully! ${response.data.message}`,
          severity: 'success',
        });

        // Refresh dashboard stats after cleanup
        setTimeout(() => {
          fetchDashboardStats();
        }, 1000);

      } else {
        console.error('❌ Cleanup failed:', response.data.message);
        setCleanupSnackbar({
          open: true,
          message: `Cleanup failed: ${response.data.message}`,
          severity: 'error',
        });
      }

    } catch (error: any) {
      console.error('💥 Cleanup error:', error);

      let errorMessage = 'Database cleanup failed due to an unexpected error.';
      if (error.response?.data?.message) {
        errorMessage = `Cleanup failed: ${error.response.data.message}`;
      } else if (error.message) {
        errorMessage = `Cleanup failed: ${error.message}`;
      }

      setCleanupSnackbar({
        open: true,
        message: errorMessage,
        severity: 'error',
      });
    } finally {
      setCleanupLoading(false);
      setCleanupDialogOpen(false);
    }
  };

  const openCleanupDialog = () => {
    console.log('🔍 Opening database cleanup dialog');
    setCleanupDialogOpen(true);
  };

  const closeCleanupDialog = () => {
    console.log('❌ Closing database cleanup dialog');
    setCleanupDialogOpen(false);
  };

  const closeSnackbar = () => {
    setCleanupSnackbar(prev => ({ ...prev, open: false }));
  };

  if (loading && !stats) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error && !stats) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!stats) {
    return (
      <Alert severity="warning" sx={{ m: 2 }}>
        No dashboard data available.
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" fontWeight="bold" gutterBottom>
            System Overview
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Monitor and manage your entire system from this central dashboard
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Typography variant="caption" color="text.secondary">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </Typography>
          <Tooltip title="Refresh Data">
            <IconButton onClick={handleRefresh} disabled={loading}>
              <Refresh />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {error && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ 
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', 
            color: 'white',
            height: '100%',
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {stats.totalOrganizations}
                  </Typography>
                  <Typography variant="h6">
                    Organizations
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                  <Business fontSize="large" />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ 
            background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', 
            color: 'white',
            height: '100%',
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {stats.totalEntityAdmins}
                  </Typography>
                  <Typography variant="h6">
                    Entity Admins
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                  <People fontSize="large" />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ 
            background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', 
            color: 'white',
            height: '100%',
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {stats.totalSuperAdmins}
                  </Typography>
                  <Typography variant="h6">
                    Super Admins
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                  <Security fontSize="large" />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ 
            background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', 
            color: 'white',
            height: '100%',
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {stats.totalNfcCards}
                  </Typography>
                  <Typography variant="h6">
                    NFC Cards
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                  <Nfc fontSize="large" />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* System Health and Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Assessment color="primary" />
                System Health
              </Typography>
              
              <Box sx={{ mt: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <CheckCircle color="success" />
                  <Typography variant="body1" fontWeight="medium">
                    System Status: Healthy
                  </Typography>
                  <Chip label="ONLINE" color="success" size="small" />
                </Box>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    System Uptime: {stats.systemHealth.uptime}
                  </Typography>
                  <LinearProgress 
                    variant="determinate" 
                    value={99.9} 
                    sx={{ height: 8, borderRadius: 4 }}
                    color="success"
                  />
                </Box>
                
                <Typography variant="body2" color="text.secondary">
                  Last Backup: {stats.systemHealth.lastBackup}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <TrendingUp color="primary" />
                System Metrics
              </Typography>
              
              <Grid container spacing={2} sx={{ mt: 1 }}>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'background.paper', borderRadius: 2 }}>
                    <Typography variant="h5" fontWeight="bold" color="primary">
                      {stats.systemMetrics.totalUsers}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Total Users
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'background.paper', borderRadius: 2 }}>
                    <Typography variant="h5" fontWeight="bold" color="secondary">
                      {stats.systemMetrics.totalSessions}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Active Sessions
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'background.paper', borderRadius: 2 }}>
                    <Typography variant="h5" fontWeight="bold" color="success.main">
                      {stats.systemMetrics.totalOrders}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Total Orders
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'background.paper', borderRadius: 2 }}>
                    <Typography variant="h5" fontWeight="bold" color="warning.main">
                      {stats.systemMetrics.systemLoad}%
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      System Load
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Database Management Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <Card sx={{
            border: '2px solid',
            borderColor: 'error.main',
            bgcolor: 'error.light',
            color: 'error.contrastText',
          }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Warning color="inherit" />
                ⚠️ DANGER ZONE - Database Management
              </Typography>

              <Typography variant="body2" sx={{ mb: 3, opacity: 0.9 }}>
                This section contains critical database operations that cannot be undone.
                Use with extreme caution and only when absolutely necessary.
              </Typography>

              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 2,
                p: 2,
                bgcolor: 'rgba(255,255,255,0.1)',
                borderRadius: 2,
              }}>
                <DeleteForever fontSize="large" />
                <Box sx={{ flex: 1 }}>
                  <Typography variant="subtitle1" fontWeight="bold">
                    Clean Database
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.8 }}>
                    Delete ALL data from ALL tables except SuperAdmins. This will permanently remove:
                    Organizations, Entity Admins, Subscribers, NFC Cards, Sessions, Orders, and all other data.
                  </Typography>
                </Box>
                <Button
                  variant="contained"
                  color="error"
                  size="large"
                  onClick={openCleanupDialog}
                  disabled={cleanupLoading}
                  startIcon={cleanupLoading ? <CircularProgress size={20} /> : <DeleteForever />}
                  sx={{
                    minWidth: 200,
                    fontWeight: 'bold',
                    '&:hover': {
                      bgcolor: 'error.dark',
                    }
                  }}
                >
                  {cleanupLoading ? 'Cleaning...' : 'Clean Database'}
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Recent Activity */}
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Schedule color="primary" />
                Recent System Activity
              </Typography>
              
              <List>
                {stats.recentActivity.map((activity, index) => (
                  <React.Fragment key={activity.id}>
                    <ListItem>
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: `${getActivityColor(activity.severity)}.main` }}>
                          {getActivityIcon(activity.type)}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={activity.message}
                        secondary={activity.timestamp}
                      />
                      <Chip
                        label={activity.severity.toUpperCase()}
                        color={getActivityColor(activity.severity) as any}
                        size="small"
                        variant="outlined"
                      />
                    </ListItem>
                    {index < stats.recentActivity.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Database Cleanup Confirmation Dialog */}
      <Dialog
        open={cleanupDialogOpen}
        onClose={closeCleanupDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle sx={{
          bgcolor: 'error.main',
          color: 'error.contrastText',
          display: 'flex',
          alignItems: 'center',
          gap: 1,
        }}>
          <Warning />
          ⚠️ CRITICAL WARNING - Database Cleanup
        </DialogTitle>
        <DialogContent sx={{ mt: 2 }}>
          <DialogContentText component="div">
            <Typography variant="h6" color="error" gutterBottom>
              YOU ARE ABOUT TO DELETE ALL DATABASE DATA!
            </Typography>

            <Typography variant="body1" paragraph>
              This action will <strong>permanently delete ALL data</strong> from the following tables:
            </Typography>

            <Box component="ul" sx={{ pl: 3, mb: 2 }}>
              <li>Organizations and all their data</li>
              <li>Entity Admins and their assignments</li>
              <li>Subscribers and their profiles</li>
              <li>NFC Cards and assignments</li>
              <li>Attendance Sessions and records</li>
              <li>Orders and transaction history</li>
              <li>System logs and activity records</li>
              <li>All other application data</li>
            </Box>

            <Typography variant="body1" paragraph color="success.main">
              ✅ <strong>PRESERVED:</strong> SuperAdmins table will remain intact
            </Typography>

            <Typography variant="body1" paragraph color="error">
              ❌ <strong>THIS CANNOT BE UNDONE!</strong>
            </Typography>

            <Typography variant="body2" color="text.secondary">
              Only proceed if you are absolutely certain you want to reset the entire system
              to a clean state. This is typically used for testing or system reset purposes.
            </Typography>
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ p: 3, gap: 2 }}>
          <Button
            onClick={closeCleanupDialog}
            variant="outlined"
            size="large"
            disabled={cleanupLoading}
          >
            Cancel - Keep Data Safe
          </Button>
          <Button
            onClick={handleCleanupDatabase}
            variant="contained"
            color="error"
            size="large"
            disabled={cleanupLoading}
            startIcon={cleanupLoading ? <CircularProgress size={20} /> : <DeleteForever />}
            sx={{ minWidth: 200 }}
          >
            {cleanupLoading ? 'Deleting All Data...' : 'YES - DELETE ALL DATA'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Cleanup Result Snackbar */}
      <Snackbar
        open={cleanupSnackbar.open}
        autoHideDuration={6000}
        onClose={closeSnackbar}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={closeSnackbar}
          severity={cleanupSnackbar.severity}
          sx={{ width: '100%', fontSize: '1.1rem' }}
        >
          {cleanupSnackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default AdminDashboardOverview;
