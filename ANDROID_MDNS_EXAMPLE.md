# 📱 Android mDNS Discovery Example

## Permissions (AndroidManifest.xml)
```xml
<uses-permission android:name="android.permission.INTERNET"/>
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
<uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE"/>
```

## Kotlin Implementation
```kotlin
class NetworkDiscoveryService(private val context: Context) {
    
    private val nsdManager = context.getSystemService(Context.NSD_SERVICE) as NsdManager
    private var multicastLock: WifiManager.MulticastLock? = null
    
    fun startDiscovery(callback: (String) -> Unit) {
        // Acquire multicast lock
        val wifiManager = context.getSystemService(Context.WIFI_SERVICE) as WifiManager
        multicastLock = wifiManager.createMulticastLock("attendance_discovery")
        multicastLock?.acquire()
        
        val serviceType = "_attendanceapi._tcp"
        
        val discoveryListener = object : NsdManager.DiscoveryListener {
            override fun onServiceFound(service: NsdServiceInfo) {
                Log.d("Discovery", "Service found: ${service.serviceName}")
                if (service.serviceName.contains("RestaurantSystem")) {
                    nsdManager.resolveService(service, resolveListener(callback))
                }
            }
            
            override fun onServiceLost(service: NsdServiceInfo) {
                Log.d("Discovery", "Service lost: ${service.serviceName}")
            }
            
            override fun onDiscoveryStarted(serviceType: String) {
                Log.d("Discovery", "Discovery started for: $serviceType")
            }
            
            override fun onDiscoveryStopped(serviceType: String) {
                Log.d("Discovery", "Discovery stopped for: $serviceType")
            }
            
            override fun onStartDiscoveryFailed(serviceType: String, errorCode: Int) {
                Log.e("Discovery", "Start discovery failed: $errorCode")
            }
            
            override fun onStopDiscoveryFailed(serviceType: String, errorCode: Int) {
                Log.e("Discovery", "Stop discovery failed: $errorCode")
            }
        }
        
        nsdManager.discoverServices(serviceType, NsdManager.PROTOCOL_DNS_SD, discoveryListener)
    }
    
    private fun resolveListener(callback: (String) -> Unit) = object : NsdManager.ResolveListener {
        override fun onServiceResolved(service: NsdServiceInfo) {
            val host = service.host.hostAddress
            val port = service.port
            val baseUrl = "http://$host:$port"
            
            Log.d("Discovery", "Service resolved: $baseUrl")
            callback(baseUrl)
        }
        
        override fun onResolveFailed(service: NsdServiceInfo, errorCode: Int) {
            Log.e("Discovery", "Resolve failed: $errorCode")
            // Fallback to manual IP entry
            callback("http://************:8080")
        }
    }
    
    fun stopDiscovery() {
        multicastLock?.release()
    }
}
```

## Usage in Activity
```kotlin
class MainActivity : AppCompatActivity() {
    
    private lateinit var discoveryService: NetworkDiscoveryService
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        discoveryService = NetworkDiscoveryService(this)
        
        // Start discovery
        discoveryService.startDiscovery { baseUrl ->
            runOnUiThread {
                // Configure your API client with discovered URL
                ApiClient.configure(baseUrl)
                Toast.makeText(this, "Server found: $baseUrl", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        discoveryService.stopDiscovery()
    }
}
```

## API Client Configuration
```kotlin
object ApiClient {
    private var baseUrl = "http://************:8080" // Fallback
    
    fun configure(url: String) {
        baseUrl = url
        // Reconfigure Retrofit/OkHttp with new base URL
    }
    
    fun getBaseUrl() = baseUrl
}
```

## Manual Fallback UI
```kotlin
// If discovery fails, show manual entry dialog
private fun showManualEntryDialog() {
    val builder = AlertDialog.Builder(this)
    val input = EditText(this)
    input.hint = "Enter server IP (e.g., ************)"
    
    builder.setTitle("Enter Server Address")
        .setView(input)
        .setPositiveButton("Connect") { _, _ ->
            val ip = input.text.toString()
            val url = "http://$ip:8080"
            ApiClient.configure(url)
        }
        .setNegativeButton("Cancel", null)
        .show()
}
```
